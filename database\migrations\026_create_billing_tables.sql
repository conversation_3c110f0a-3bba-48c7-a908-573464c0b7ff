-- Create billing module tables

-- Billing rates table (service pricing)
CREATE TABLE IF NOT EXISTS billing_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_code VARCHAR(50) UNIQUE NOT NULL,
    service_name VARCHAR(255) NOT NULL,
    service_description TEXT,
    rate DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) DEFAULT 'each',
    category VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_billing_rates_code (service_code),
    INDEX idx_billing_rates_category (category),
    INDEX idx_billing_rates_active (is_active)
);

-- Billing events table (service usage logs)
CREATE TABLE IF NOT EXISTS billing_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    client_id INT NOT NULL,
    service_code VARCHAR(50) NOT NULL,
    
    -- Event details
    event_type VARCHAR(100) NOT NULL,
    quantity DECIMAL(10,2) DEFAULT 1.00,
    unit_rate DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    
    -- Context information
    entity_type VARCHAR(50), -- 'box', 'bundle', 'document', etc.
    entity_id INT,
    reference_number VARCHAR(100),
    
    -- Billing status
    billing_status ENUM('pending', 'invoiced', 'paid', 'disputed', 'cancelled') DEFAULT 'pending',
    invoice_id INT NULL,
    
    -- Audit information
    performed_by INT NOT NULL,
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    
    -- Metadata
    metadata JSON,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (service_code) REFERENCES billing_rates(service_code),
    FOREIGN KEY (performed_by) REFERENCES users(id),
    
    INDEX idx_billing_events_company (company_id),
    INDEX idx_billing_events_client (client_id),
    INDEX idx_billing_events_service (service_code),
    INDEX idx_billing_events_date (performed_at),
    INDEX idx_billing_events_status (billing_status),
    INDEX idx_billing_events_invoice (invoice_id)
);

-- Billing invoices table
CREATE TABLE IF NOT EXISTS billing_invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    client_id INT NOT NULL,
    
    -- Invoice details
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    
    -- Billing period
    billing_period_start DATE NOT NULL,
    billing_period_end DATE NOT NULL,
    
    -- Financial details
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    tax_rate DECIMAL(5,4) DEFAULT 0.0000,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    
    -- Payment tracking
    payment_status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled', 'disputed') DEFAULT 'draft',
    payment_date DATE NULL,
    payment_method VARCHAR(50) NULL,
    payment_reference VARCHAR(100) NULL,
    
    -- Invoice content
    line_items JSON NOT NULL,
    invoice_notes TEXT,
    
    -- File storage
    pdf_file_path VARCHAR(500),
    
    -- Audit
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_billing_invoices_company (company_id),
    INDEX idx_billing_invoices_client (client_id),
    INDEX idx_billing_invoices_number (invoice_number),
    INDEX idx_billing_invoices_date (invoice_date),
    INDEX idx_billing_invoices_status (payment_status),
    INDEX idx_billing_invoices_period (billing_period_start, billing_period_end)
);

-- Billing payments table (payment tracking)
CREATE TABLE IF NOT EXISTS billing_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    company_id INT NOT NULL,
    client_id INT NOT NULL,
    
    -- Payment details
    payment_amount DECIMAL(12,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_reference VARCHAR(100),
    
    -- Bank/transaction details
    bank_reference VARCHAR(100),
    transaction_id VARCHAR(100),
    
    -- Status
    payment_status ENUM('pending', 'cleared', 'bounced', 'refunded') DEFAULT 'pending',
    
    -- Notes
    notes TEXT,
    
    -- Audit
    recorded_by INT NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES billing_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id),
    
    INDEX idx_billing_payments_invoice (invoice_id),
    INDEX idx_billing_payments_client (client_id),
    INDEX idx_billing_payments_date (payment_date),
    INDEX idx_billing_payments_status (payment_status)
);

-- Update billing_events to reference invoices
ALTER TABLE billing_events 
ADD CONSTRAINT fk_billing_events_invoice 
FOREIGN KEY (invoice_id) REFERENCES billing_invoices(id) ON SET NULL;

-- Insert default billing rates
INSERT INTO billing_rates (service_code, service_name, service_description, rate, unit, category) VALUES
-- Document Processing
('intake.new', 'New Intake', 'Processing new document intake request', 80.00, 'each', 'processing'),
('bundle.handling', 'Bundle/File Handling', 'Processing and organizing document bundles', 5.00, 'each', 'processing'),
('box.handling', 'Box Handling', 'Physical box processing and management', 5.00, 'each', 'processing'),
('box.packing', 'Box Packing/Repacking', 'Packing or repacking documents in boxes', 10.00, 'each', 'processing'),
('box.registration', 'Box Registration (Indexing)', 'Registering and indexing box contents', 2.00, 'each', 'processing'),

-- Search Services
('search.box', 'Search Box', 'Searching for specific boxes', 5.00, 'each', 'search'),
('search.bundle', 'Search Bundle/File', 'Searching for specific bundles or files', 5.00, 'each', 'search'),

-- Delivery Services
('delivery.to_client', 'Deliver to Client', 'Delivery of boxes/bundles/files to client', 80.00, 'each', 'delivery'),
('collection.from_client', 'Collect from Client', 'Collection of boxes/bundles/files from client', 80.00, 'each', 'delivery'),

-- Storage Services (Monthly)
('storage.small_box', 'Small Box Storage', 'Monthly storage fee for small boxes', 1.00, 'month', 'storage'),
('storage.medium_box', 'Medium Box Storage', 'Monthly storage fee for medium boxes', 1.50, 'month', 'storage'),
('storage.large_box', 'Large Box Storage', 'Monthly storage fee for large boxes', 2.00, 'month', 'storage'),

-- Barcode Services
('barcode.box', 'Box Barcode', 'Generate barcode for box', 0.20, 'each', 'barcode'),
('barcode.bundle', 'Bundle Barcode', 'Generate barcode for bundle', 0.15, 'each', 'barcode'),

-- Special Services
('withdrawal.permanent', 'Permanent Withdrawal', 'Permanent removal of documents from storage', 10.00, 'each', 'special'),
('destruction.per_kg', 'Document Destruction', 'Secure destruction of documents', 3.00, 'kg', 'special');
