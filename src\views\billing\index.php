<?php
$title = 'Billing Dashboard';
$currentPage = 'billing';
include __DIR__ . '/../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Billing Dashboard</h1>
                <div>
                    <a href="/app/billing/rates" class="btn btn-outline-primary me-2">
                        <i class="fas fa-list"></i> Service Rates
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateInvoiceModal">
                        <i class="fas fa-file-invoice"></i> Generate Invoice
                    </button>
                </div>
            </div>

            <!-- Current Period Summary -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Current Period: <?= htmlspecialchars($current_period) ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php 
                                $totalRunning = 0;
                                $totalEvents = 0;
                                foreach ($client_stats as $stat): 
                                    $totalRunning += $stat['running_total']['total_amount'];
                                    $totalEvents += $stat['running_total']['event_count'];
                                endforeach;
                                ?>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-primary">$<?= number_format($totalRunning, 2) ?></h3>
                                        <p class="text-muted mb-0">Total Unbilled</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-info"><?= $totalEvents ?></h3>
                                        <p class="text-muted mb-0">Billable Events</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-success"><?= count($client_stats) ?></h3>
                                        <p class="text-muted mb-0">Active Clients</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-warning"><?= count($recent_invoices) ?></h3>
                                        <p class="text-muted mb-0">Recent Invoices</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Billing Summary -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Client Billing Summary</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($client_stats)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No client billing data available</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Client</th>
                                                <th>Current Period</th>
                                                <th>Events</th>
                                                <th>Running Total</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($client_stats as $stat): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($stat['client']['name']) ?></strong>
                                                    </td>
                                                    <td><?= htmlspecialchars($stat['running_total']['period']) ?></td>
                                                    <td>
                                                        <span class="badge bg-info"><?= $stat['running_total']['event_count'] ?></span>
                                                    </td>
                                                    <td>
                                                        <strong class="text-primary">$<?= number_format($stat['running_total']['total_amount'], 2) ?></strong>
                                                    </td>
                                                    <td>
                                                        <a href="/app/billing/client/<?= $stat['client']['id'] ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i> View Details
                                                        </a>
                                                        <?php if ($stat['running_total']['event_count'] > 0): ?>
                                                            <button type="button" class="btn btn-sm btn-success ms-1" 
                                                                    onclick="generateInvoiceForClient(<?= $stat['client']['id'] ?>, '<?= htmlspecialchars($stat['client']['name']) ?>')">
                                                                <i class="fas fa-file-invoice"></i> Invoice
                                                            </button>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Invoices -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Recent Invoices</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_invoices)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No invoices generated yet</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Invoice #</th>
                                                <th>Client</th>
                                                <th>Date</th>
                                                <th>Period</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_invoices as $invoice): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($invoice['invoice_number']) ?></strong>
                                                    </td>
                                                    <td><?= htmlspecialchars($invoice['client_name']) ?></td>
                                                    <td><?= date('M j, Y', strtotime($invoice['invoice_date'])) ?></td>
                                                    <td>
                                                        <?= date('M j', strtotime($invoice['billing_period_start'])) ?> - 
                                                        <?= date('M j, Y', strtotime($invoice['billing_period_end'])) ?>
                                                    </td>
                                                    <td>
                                                        <strong>$<?= number_format($invoice['total_amount'], 2) ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $statusClass = [
                                                            'draft' => 'secondary',
                                                            'sent' => 'info',
                                                            'paid' => 'success',
                                                            'overdue' => 'danger',
                                                            'cancelled' => 'dark',
                                                            'disputed' => 'warning'
                                                        ];
                                                        $class = $statusClass[$invoice['payment_status']] ?? 'secondary';
                                                        ?>
                                                        <span class="badge bg-<?= $class ?>"><?= ucfirst($invoice['payment_status']) ?></span>
                                                    </td>
                                                    <td>
                                                        <a href="/app/billing/invoice/<?= $invoice['id'] ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generate Invoice Modal -->
<div class="modal fade" id="generateInvoiceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="/app/billing/generate-invoice">
                <div class="modal-header">
                    <h5 class="modal-title">Generate Invoice</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="client_id" class="form-label">Client</label>
                        <select class="form-select" id="client_id" name="client_id" required>
                            <option value="">Select a client...</option>
                            <?php foreach ($client_stats as $stat): ?>
                                <option value="<?= $stat['client']['id'] ?>"><?= htmlspecialchars($stat['client']['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="year" class="form-label">Year</label>
                                <select class="form-select" id="year" name="year" required>
                                    <?php for ($y = date('Y'); $y >= date('Y') - 2; $y--): ?>
                                        <option value="<?= $y ?>" <?= $y == date('Y') ? 'selected' : '' ?>><?= $y ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="month" class="form-label">Month</label>
                                <select class="form-select" id="month" name="month" required>
                                    <?php 
                                    $months = [
                                        1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                                        5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                                        9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
                                    ];
                                    foreach ($months as $num => $name): 
                                    ?>
                                        <option value="<?= $num ?>" <?= $num == date('n') ? 'selected' : '' ?>><?= $name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Generate Invoice</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function generateInvoiceForClient(clientId, clientName) {
    document.getElementById('client_id').value = clientId;
    const modal = new bootstrap.Modal(document.getElementById('generateInvoiceModal'));
    modal.show();
}
</script>

<?php include __DIR__ . '/../layouts/footer.php'; ?>
