<?php
/**
 * Web-based Billing Module Setup
 * 
 * This page allows you to run the billing migration through a web browser
 */

// Security check - only allow from localhost or if specifically enabled
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($clientIP, $allowedIPs) && !isset($_GET['allow_remote'])) {
    die('Access denied. This setup page can only be accessed from localhost for security reasons.');
}

require_once __DIR__ . '/../src/autoload.php';
require_once __DIR__ . '/../src/config/database.php';

use App\Core\Database;

$output = [];
$success = false;
$error = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    try {
        $output[] = "Starting Billing Module Migration...";
        
        $db = Database::getInstance();
        
        // Read and execute the billing migration
        $migrationFile = __DIR__ . '/../database/migrations/026_create_billing_tables.sql';
        
        if (!file_exists($migrationFile)) {
            throw new Exception("Migration file not found: {$migrationFile}");
        }
        
        $sql = file_get_contents($migrationFile);
        
        // Split the SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );
        
        $output[] = "Executing " . count($statements) . " SQL statements...";
        
        foreach ($statements as $index => $statement) {
            try {
                if (trim($statement)) {
                    $db->execute($statement);
                    $output[] = "✓ Statement " . ($index + 1) . " executed successfully";
                }
            } catch (Exception $e) {
                // Check if it's a "table already exists" error - we can ignore those
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    $output[] = "⚠ Statement " . ($index + 1) . " skipped (already exists)";
                } else {
                    throw new Exception("Error in statement " . ($index + 1) . ": " . $e->getMessage());
                }
            }
        }
        
        // Verify tables were created
        $output[] = "";
        $output[] = "Verifying billing tables...";
        
        $requiredTables = [
            'billing_rates',
            'billing_events', 
            'billing_invoices',
            'billing_payments'
        ];
        
        foreach ($requiredTables as $table) {
            $exists = $db->fetch("SHOW TABLES LIKE '{$table}'");
            if ($exists) {
                $output[] = "✓ Table '{$table}' exists";
            } else {
                throw new Exception("Table '{$table}' was not created");
            }
        }
        
        // Check if billing rates were inserted
        $rateCount = $db->fetch("SELECT COUNT(*) as count FROM billing_rates");
        $output[] = "✓ Billing rates table has {$rateCount['count']} service rates";
        
        // Display some sample rates
        $output[] = "";
        $output[] = "Sample billing rates:";
        $sampleRates = $db->fetchAll(
            "SELECT service_code, service_name, rate, unit, category 
             FROM billing_rates 
             WHERE is_active = 1 
             ORDER BY category, service_name 
             LIMIT 10"
        );
        
        foreach ($sampleRates as $rate) {
            $output[] = sprintf(
                "  - %s: $%s per %s (%s)",
                $rate['service_name'],
                number_format($rate['rate'], 2),
                $rate['unit'],
                $rate['category']
            );
        }
        
        $output[] = "";
        $output[] = "✅ Billing Module Migration completed successfully!";
        $output[] = "";
        $output[] = "Next steps:";
        $output[] = "1. Access the billing dashboard at: /app/billing";
        $output[] = "2. Review and adjust service rates if needed";
        $output[] = "3. Start logging billing events in your application";
        $output[] = "4. Generate your first invoice at month-end";
        
        $success = true;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        $output[] = "";
        $output[] = "❌ Migration failed: " . $error;
        $output[] = "Please check your database connection and try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billing Module Setup - DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .output-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-line;
        }
        .success { color: #198754; }
        .warning { color: #fd7e14; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database"></i> Billing Module Setup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (!$success && !$error): ?>
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Ready to Setup Billing Module</h5>
                                <p>This will create the necessary database tables and insert default billing rates for your document management system.</p>
                                <p><strong>What this will do:</strong></p>
                                <ul>
                                    <li>Create billing_rates table with 25+ predefined service rates</li>
                                    <li>Create billing_events table for tracking billable activities</li>
                                    <li>Create billing_invoices table for invoice management</li>
                                    <li>Create billing_payments table for payment tracking</li>
                                </ul>
                            </div>

                            <form method="POST">
                                <div class="d-grid">
                                    <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                        <i class="fas fa-play"></i> Run Billing Module Setup
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> Setup Completed Successfully!</h5>
                                <p>The billing module has been installed and configured.</p>
                            </div>

                            <div class="mb-3">
                                <h6>Quick Links:</h6>
                                <a href="/app/billing" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-tachometer-alt"></i> Billing Dashboard
                                </a>
                                <a href="/app/billing/rates" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-list"></i> Service Rates
                                </a>
                                <a href="../docs/BILLING_MODULE_IMPLEMENTATION.md" class="btn btn-outline-info">
                                    <i class="fas fa-book"></i> Documentation
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle"></i> Setup Failed</h5>
                                <p><strong>Error:</strong> <?= htmlspecialchars($error) ?></p>
                                <p>Please check your database configuration and try again.</p>
                            </div>

                            <div class="d-grid">
                                <a href="<?= $_SERVER['PHP_SELF'] ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> Try Again
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($output)): ?>
                            <div class="mt-4">
                                <h6>Setup Output:</h6>
                                <div class="output-box">
                                    <?php foreach ($output as $line): ?>
                                        <?php
                                        $class = '';
                                        if (strpos($line, '✓') === 0) $class = 'success';
                                        elseif (strpos($line, '⚠') === 0) $class = 'warning';
                                        elseif (strpos($line, '❌') === 0) $class = 'error';
                                        ?>
                                        <div class="<?= $class ?>"><?= htmlspecialchars($line) ?></div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mt-4 pt-3 border-top">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i> 
                                This setup page is only accessible from localhost for security reasons.
                                After setup is complete, you can safely delete this file.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
